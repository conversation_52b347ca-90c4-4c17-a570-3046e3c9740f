/* Sidebar.css */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 250px;
  min-width: 250px;
  max-width: 250px;
  background-color: #1e3a8a;
  color: #ffffff;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  z-index: 50;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  transition: transform 0.3s ease;
  box-sizing: border-box;
  border-right: none;
  margin-right: 0;
  padding-right: 0;
}

/* Sidebar Header */
.sidebar-header {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #ffffff;
}

.sidebar-logo-img {
  width: 40px;
  height: 40px;
  margin-right: 10px;
  border-radius: 50%;
  object-fit: cover;
}

.sidebar-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
}

/* Sidebar Navigation */
.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu-item {
  margin-bottom: 0.25rem;
}

.sidebar-menu-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.sidebar-menu-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border-left-color: #ffffff;
}

.sidebar-menu-link.active {
  background-color: rgba(255, 255, 255, 0.15);
  color: #ffffff;
  border-left-color: #f59e0b;
  font-weight: 600;
}

.sidebar-icon {
  width: 20px;
  margin-right: 10px;
  text-align: center;
}

.sidebar-menu-divider {
  padding: 0.5rem 1.5rem;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 1rem;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.75rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: 85%;
    max-width: 300px;
    min-width: 250px;
    z-index: 1010; /* Higher than navigation */
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  /* Add overlay when sidebar is open */
  .sidebar.open::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
  }

  /* Adjust sidebar header for mobile */
  .sidebar-header {
    padding: 1.2rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  /* Make menu items larger and more touch-friendly */
  .sidebar-menu-link {
    padding: 0.9rem 1.5rem;
    font-size: 1.05rem;
  }

  /* Add close button for mobile */
  .sidebar-close {
    display: block;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
  }

  /* Hide logo on smaller devices */
  .sidebar-logo-img {
    display: none;
  }
}

/* Ensure the sidebar fills the entire height */
.site-sidebar {
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 250px;
  min-width: 250px;
  max-width: 250px;
  box-sizing: border-box;
  z-index: 40;
  border-right: none;
  margin-right: 0;
  padding-right: 0;
  overflow-x: hidden;
}
