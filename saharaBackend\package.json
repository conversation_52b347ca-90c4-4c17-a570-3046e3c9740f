{"name": "backend", "version": "1.0.0", "description": "", "main": "Controller.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "security:test": "node tests/security-tests.js", "security:monitor": "node scripts/security-monitor.js start", "security:scan": "node scripts/security-monitor.js scan", "security:health": "node scripts/security-monitor.js health"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"nodemon": "^3.1.9"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cloudinary": "^2.6.1", "cookie-parser": "^1.4.7", "cores": "^0.8.0", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "googleapis": "^148.0.0", "helmet": "^7.2.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "mammoth": "^1.9.0", "mongoose": "^8.12.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "nodemailer": "^6.10.0", "pdfkit": "^0.16.0", "puppeteer": "^24.8.0", "xss": "^1.0.15"}}