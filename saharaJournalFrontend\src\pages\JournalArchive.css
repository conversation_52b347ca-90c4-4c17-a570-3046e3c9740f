/* JournalArchive.css */

.journal-archive-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1.5rem;
}

/* Header Styling */
.archive-header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    position: relative;
}

.archive-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(to right, #1e3a8a, #3b82f6);
    border-radius: 2px;
}

.archive-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 1rem;
}

.archive-header p {
    font-size: 1.1rem;
    color: #4b5563;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Search and Filter Styling */
.search-filter-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    gap: 1rem;
}

.search-bar {
    flex: 1;
    position: relative;
    max-width: 600px;
}

.search-bar input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-bar input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
}

.filter-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-weight: 500;
    color: #4b5563;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-toggle:hover {
    background-color: #f1f5f9;
    border-color: #cbd5e1;
}

/* Filters Panel */
.filters-panel {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #4b5563;
}

.filter-icon {
    color: #6b7280;
}

.filter-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background-color: white;
    font-size: 0.95rem;
    color: #1f2937;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-group select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Results Info */
.results-info {
    margin-bottom: 1.5rem;
    color: #6b7280;
    font-size: 0.95rem;
}

/* Journals Grid */
.journals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
}

/* Journal Card */
.journal-card {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.journal-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.journal-card-content {
    padding: 1.5rem;
    flex: 1;
}

.journal-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.4;
    color: #1e3a8a;
}

.journal-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.journal-title a:hover {
    color: #3b82f6;
}

.journal-authors {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #4b5563;
    font-size: 0.95rem;
}

.card-icon {
    color: #6b7280;
    flex-shrink: 0;
}

.journal-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.journal-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
}

.journal-category {
    background-color: #e0f2fe;
    color: #0369a1;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.journal-abstract {
    color: #4b5563;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.journal-details {
    display: flex;
    gap: 1rem;
    font-size: 0.85rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
}

.journal-actions {
    display: flex;
    border-top: 1px solid #e2e8f0;
}

.view-button, .download-button {
    flex: 1;
    padding: 0.75rem;
    text-align: center;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.view-button {
    background-color: #f8fafc;
    color: #1e3a8a;
    border-right: 1px solid #e2e8f0;
}

.download-button {
    background-color: #1e3a8a;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.view-button:hover {
    background-color: #f1f5f9;
}

.download-button:hover {
    background-color: #1e40af;
}

/* Loading and Error States */
.loading-container, .error-container, .no-results {
    text-align: center;
    padding: 3rem;
    background-color: #f8fafc;
    border-radius: 8px;
    border: 1px dashed #e2e8f0;
}

.loading-spinner {
    border: 4px solid rgba(226, 232, 240, 0.3);
    border-radius: 50%;
    border-top: 4px solid #3b82f6;
    width: 40px;
    height: 40px;
    margin: 0 auto 1rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.reset-button {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background-color: #1e3a8a;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.reset-button:hover {
    background-color: #1e40af;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .archive-header h1 {
        font-size: 2rem;
    }
    
    .search-filter-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-bar {
        max-width: none;
    }
    
    .filter-toggle {
        width: 100%;
        justify-content: center;
    }
    
    .journals-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .journal-archive-container {
        padding: 1.5rem 1rem;
    }
    
    .archive-header {
        margin-bottom: 2rem;
    }
    
    .archive-header h1 {
        font-size: 1.75rem;
    }
    
    .journal-title {
        font-size: 1.1rem;
    }
}
