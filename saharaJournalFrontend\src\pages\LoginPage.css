/* General Styles */
body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
}

/* Login Container */
.login-container {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

/* Title */
.login-container h2 {
    margin-bottom: 1rem;
    font-size: 1.8rem;
    color: #333;
}

/* Form */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Label */
.login-form label {
    font-weight: bold;
    text-align: left;
    display: block;
    margin-bottom: 0.3rem;
    color: #555;
}

/* Input Fields */
.login-form input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 1rem;
    transition: border 0.3s;
}

.login-form input:focus {
    border-color: #007bff;
    outline: none;
}

/* Submit Button */
.login-form button {
    background-color: #007bff;
    color: white;
    padding: 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s;
}

.login-form button:hover {
    background-color: #0056b3;
}

.login-form button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container {
        padding: 1.5rem;
    }
}
