.editorial-board-page {
  padding: 2rem 1rem;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.page-title {
  text-align: center;
  color: #1e3a8a;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e2e8f0;
}

.section {
  margin-bottom: 2rem;
}

.section-title {
  color: #1e3a8a;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.editor {
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: #f8fafc;
  border-radius: 4px;
  border-left: 3px solid #3b82f6;
}

.editor-name {
  color: #1e3a8a;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.editor-affiliation {
  color: #4b5563;
  font-size: 1rem;
  margin: 0;
}

.publisher-info,
.contact-info {
  color: #4b5563;
  font-size: 1rem;
  line-height: 1.6;
  white-space: pre-line;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .editorial-board-page {
    padding: 1rem 0.5rem;
  }
  
  .container {
    padding: 1rem;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .section-title {
    font-size: 1.3rem;
  }
  
  .editor-name {
    font-size: 1.1rem;
  }
  
  .editor-affiliation {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.75rem;
  }
  
  .section-title {
    font-size: 1.2rem;
  }
}
