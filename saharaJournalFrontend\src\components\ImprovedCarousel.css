/* Main carousel container */
.carousel-container {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  background-color: #f8f9fa;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  height: auto;
}

/* Main title styling */
.carousel-main-title {
  background: linear-gradient(135deg, #1e3a8a 0%, #2563eb 100%);
  padding: 1rem;
  text-align: center;
}

.carousel-main-title h1 {
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Inner container for image and content */
.carousel-inner-container {
  display: flex;
  flex-direction: row;
  height: 500px;
}

/* Image container styling */
.carousel-image-container {
  flex: 0 0 60%;
  position: relative;
  height: 100%;
  overflow: hidden;
  background-color: #111827;
}

/* Slide styling */
.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.7s ease-in-out, visibility 0.7s ease-in-out;
}

.carousel-slide.active {
  opacity: 1;
  visibility: visible;
}

/* Image styling */
.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

/* Error overlay styling */
.carousel-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(17, 24, 39, 0.8);
}

.carousel-error-content {
  text-align: center;
  padding: 1.5rem;
  color: white;
}

.carousel-error-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.carousel-error-description {
  font-size: 1rem;
}

/* Content container styling */
.carousel-content {
  flex: 0 0 40%;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: #1e3a8a;
  position: relative;
}

/* Caption styling */
.carousel-caption {
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.carousel-caption-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
  line-height: 1.3;
  border-bottom: 2px solid #3b82f6;
  padding-bottom: 0.5rem;
}

.carousel-caption-text {
  font-size: 1.1rem;
  color: white;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

/* Call-to-action button */
.carousel-cta-button {
  background: linear-gradient(to right, #1e3a8a, #3b82f6);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.carousel-cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

/* Navigation buttons */
.carousel-nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(255, 255, 255, 0.7);
  color: #1e3a8a;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 20;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.carousel-nav-button:hover {
  background-color: white;
  transform: translateY(-50%) scale(1.1);
}

.carousel-nav-button.prev {
  left: 1rem;
}

.carousel-nav-button.next {
  right: 1rem;
}

/* Indicator dots */
.carousel-dots {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  margin-top: auto;
}

.carousel-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(30, 58, 138, 0.3);
  border: 2px solid #1e3a8a;
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-dot.active {
  background-color: #1e3a8a;
  transform: scale(1.2);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .carousel-inner-container {
    flex-direction: column;
    height: auto;
  }

  .carousel-image-container,
  .carousel-content {
    flex: 0 0 100%;
  }

  .carousel-image-container {
    height: 450px;
  }

  .carousel-content {
    padding: 1.5rem;
  }

  .carousel-caption-title {
    font-size: 1.5rem;
  }

  .carousel-caption-text {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .carousel-inner-container {
    flex-direction: column;
    height: auto;
  }

  .carousel-image-container,
  .carousel-content {
    flex: 0 0 100%;
  }

  .carousel-image-container {
    height: 500px;
  }

  .carousel-content {
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  }

  .carousel-caption {
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 0.75rem;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin: 1rem;
  }

  .carousel-caption-title {
    font-size: 1.75rem;
    margin-bottom: 1rem;
  }

  .carousel-caption-text {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
  }

  .carousel-cta-button {
    padding: 0.875rem 1.75rem;
    font-size: 1rem;
  }
  
  .carousel-main-title h1 {
    font-size: 1.25rem;
  }

  .carousel-nav-button {
    width: 32px;
    height: 32px;
  }
}

/* Hide content on smaller devices to only show images */
@media (max-width: 576px) {
  .carousel-content {
    display: none;
  }
  
  .carousel-image-container {
    height: 400px;
  }
  
  .carousel-dots {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .carousel-image-container {
    height: 400px;
  }

  .carousel-content {
    padding: 1rem;
  }

  .carousel-caption {
    padding: 1rem;
    margin: 0.75rem;
  }

  .carousel-caption-title {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .carousel-caption-text {
    font-size: 1rem;
    margin-bottom: 1.25rem;
  }

  .carousel-cta-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
  
  .carousel-main-title h1 {
    font-size: 1rem;
  }

  .carousel-nav-button {
    width: 28px;
    height: 28px;
  }
}
