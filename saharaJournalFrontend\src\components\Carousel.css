.carousel-caption {
    position: absolute;
    bottom: 20px; /* Adjust as necessary */
    left: 15%; /* Center the text */
    right: 15%; /* Center the text */
    background-color: rgba(0, 0, 0, 0.7); /* Semi-transparent black background */
    color: #fff; /* White text color */
    padding: 10px 20px; /* Add padding around the text */
    border-radius: 5px; /* Rounded corners for better aesthetics */
    max-width: 80%; /* Limit the caption width for readability */
    margin: 0 auto; /* Center align the caption */
    text-align: center; /* Center the text */
}

.carousel-caption h5 {
    font-size: 2rem; /* Default size for heading text */
    font-weight: bold; /* Bold font for the heading */
}

.carousel-caption p {
    font-size: 1.25rem; /* Default paragraph text size */
    margin-top: 5px; /* Add spacing between heading and paragraph */
}

/* Responsive styles */
@media (max-width: 768px) {
    .carousel-caption h5 {
        font-size: 1.5rem; /* Smaller size for medium devices */
    }
    .carousel-caption p {
        font-size: 1rem; /* Smaller size for medium devices */
    }
}

@media (max-width: 576px) {
    .carousel-caption {
        display: none; /* Hides the captions on small devices */
    }
    .carousel-caption h5 {
        font-size: 1.2rem; /* Smaller size for small devices */
    }
    .carousel-caption p {
        font-size: 0.9rem; /* Smaller size for small devices */
    }
}