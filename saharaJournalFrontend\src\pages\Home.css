/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body, html {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  scroll-behavior: smooth;
  color: #333;
  background-color: #f8f9fa;
}

/* Layout */
.min-h-screen {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231e3a8a' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.main-content {
  width: 100%;
  max-width: 1280px;
  padding: 0 0 60px 0;
  margin: 0 auto;
}

/* Carousel Section */
.home-carousel-wrapper {
  width: calc(100% - 40px);
  max-width: 1280px;
  margin: 2rem auto;
  overflow: hidden;
  display: flex;
  justify-content: center;
}

.carousel {
  width: 100%;
  max-height: 500px;
  position: relative;
}

.carousel-inner {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.carousel-item {
  position: relative;
  display: none;
  width: 100%;
  height: 100%;
  transition: transform 0.6s ease-in-out;
}

.carousel-item.active {
  display: block;
}

.carousel-item img {
  width: 100%;
  height: 500px;
  object-fit: cover;
  border-radius: 4px;
  display: block;
}

.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 40px;
  left: 15%;
  z-index: 10;
  padding: 15px;
  color: #fff;
  text-align: center;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 8px;
  max-width: 80%;
  margin: 0 auto;
}

.carousel-caption h5 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.carousel-control-prev,
.carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 5%;
  opacity: 0.8;
  color: #fff;
  background: none;
  border: 0;
  transition: opacity 0.15s ease;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
  opacity: 1;
}

.carousel-control-prev {
  left: 0;
}

.carousel-control-next {
  right: 0;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 100% 100%;
}

.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 20px;
  left: 0;
  z-index: 15;
  display: flex;
  justify-content: center;
  padding-left: 0;
  margin: 0 15%;
  list-style: none;
}

.carousel-indicators li {
  box-sizing: content-box;
  flex: 0 1 auto;
  width: 12px;
  height: 12px;
  margin: 0 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border-radius: 50%;
  opacity: .5;
  transition: opacity .6s ease;
}

.carousel-indicators .active {
  opacity: 1;
}

/* Welcome Header Section */
.welcome-header {
  text-align: center;
  padding: 3.5rem 2.5rem;
  max-width: 1280px;
  margin: 0 auto 3rem auto;
  width: calc(100% - 40px);
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.95), rgba(37, 99, 235, 0.9));
  border-radius: 16px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  position: relative;
  overflow: hidden;
}

.welcome-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(to right, #ffffff, #93c5fd, #ffffff);
  opacity: 0.8;
}

.welcome-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.welcome-description {
  font-size: 1.2rem;
  color: #ffffff;
  margin-bottom: 2rem;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.submit-button {
  display: inline-block;
  background: #ffffff;
  color: #1e3a8a;
  border: none;
  padding: 12px 28px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  background: #f8f9fa;
  color: #1e40af;
  text-decoration: none;
}

/* Featured Section */
.featured-section {
  background: white;
  border-radius: 12px;
  margin: 2rem auto;
  padding: 2.5rem;
  width: calc(100% - 40px);
  max-width: 1280px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  text-align: center;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.featured-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e3a8a;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 15px;
}

.featured-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background: linear-gradient(to right, #1e3a8a, #2563eb);
}

.view-all-link {
  text-align: center;
  margin-top: 2rem;
}

/* Journal List Component */
.journal-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin: 2rem auto;
  max-width: 1280px;
  width: calc(100% - 40px);
}

.journal-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.4s ease;
  border: 1px solid rgba(226, 232, 240, 0.7);
  position: relative;
}

.journal-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, #1e3a8a, #3b82f6);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.journal-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.journal-item:hover::after {
  transform: scaleX(1);
}

.journal-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.journal-item-content {
  padding: 1.75rem;
  position: relative;
}

.journal-item h3 {
  font-size: 1.25rem;
  margin-bottom: 0.75rem;
  color: #1e3a8a;
  font-weight: 700;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.journal-item:hover h3 {
  color: #2563eb;
}

.journal-item p {
  color: #4b5563;
  margin-bottom: 1.25rem;
  line-height: 1.6;
  font-size: 0.95rem;
}

.journal-item-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.85rem;
  color: #6b7280;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(226, 232, 240, 0.8);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-header {
  animation: slideUp 1s ease-out;
}

.journal-item {
  animation: fadeIn 0.6s ease-out both;
}

.journal-list .journal-item:nth-child(1) { animation-delay: 0.1s; }
.journal-list .journal-item:nth-child(2) { animation-delay: 0.2s; }
.journal-list .journal-item:nth-child(3) { animation-delay: 0.3s; }
.journal-list .journal-item:nth-child(4) { animation-delay: 0.4s; }
.journal-list .journal-item:nth-child(5) { animation-delay: 0.5s; }
.journal-list .journal-item:nth-child(6) { animation-delay: 0.6s; }

/* Links */
a {
  color: #2563eb;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #1e40af;
  text-decoration: underline;
}

.text-blue-600 {
  color: #2563eb;
  font-weight: 600;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  position: relative;
  display: inline-block;
}

.text-blue-600:hover {
  color: #1e40af;
  transform: translateY(-2px);
}

.text-blue-600:after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background-color: #1e40af;
  transform: translateX(-50%);
  transition: width 0.3s ease;
}

.text-blue-600:hover:after {
  width: 80%;
}

/* Utility Classes */
.text-center { text-align: center; }
.font-medium { font-weight: 500; }
.hover:underline:hover { text-decoration: underline; }

/* Responsive Design */
@media (max-width: 1280px) {
  .home-carousel-wrapper,
  .journal-list,
  .welcome-header,
  .featured-section,
  .main-content {
    max-width: 95%;
    width: calc(100% - 30px);
  }
}

@media (max-width: 992px) {
  .main-content {
    padding: 0 1.5rem 60px 1.5rem;
  }

  .welcome-title {
    font-size: 2rem;
  }

  .carousel-item img {
    height: 500px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 0 0 60px 0;
  }

  .home-carousel-wrapper,
  .journal-list,
  .welcome-header,
  .featured-section {
    width: calc(100% - 30px);
  }

  .home-carousel-wrapper {
    margin: 1.5rem auto;
  }

  .welcome-header {
    padding: 4rem 1.5rem;
  }

  .welcome-title {
    font-size: 1.8rem;
  }

  .welcome-description {
    font-size: 1rem;
  }

  .submit-button {
    padding: 10px 22px;
    font-size: 0.9rem;
  }

  .journal-list {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .featured-section {
    padding: 1.5rem;
    margin: 1.5rem auto;
  }

  .featured-title {
    font-size: 1.75rem;
  }
}

@media (max-width: 576px) {
  .home-carousel-wrapper,
  .journal-list,
  .welcome-header,
  .featured-section {
    width: calc(100% - 20px);
  }

  .home-carousel-wrapper {
    margin: 0.75rem auto;
  }

  .carousel-item img {
    height: 400px;
  }

  .welcome-header {
    padding: 3rem 1rem;
  }

  .welcome-title {
    font-size: 1.5rem;
  }

  .featured-section {
    padding: 1.25rem;
    margin: 1rem auto;
  }

  .featured-title {
    font-size: 1.5rem;
  }

  .journal-list {
    grid-template-columns: 1fr;
  }
}