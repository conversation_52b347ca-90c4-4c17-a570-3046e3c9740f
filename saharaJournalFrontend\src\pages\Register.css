/* Register Page Styles */
.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.min-h-screen {
    min-height: 100vh; /* Full height of the viewport */
}

.bg-gray-100 {
    background-color: #f7fafc; /* Light gray background */
}

.max-w-md {
    max-width: 28rem; /* Max width for the form container */
}

.w-full {
    width: 100%; /* Full width for inputs */
}

.p-6 {
    padding: 1.5rem; /* Padding for the form container */
}

.bg-white {
    background-color: #ffffff; /* White background for form */
}

.shadow-md {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Slight shadow for depth */
}

.rounded-md {
    border-radius: 0.375rem; /* Rounded corners */
}

.text-2xl {
    font-size: 1.5rem; /* Size for the form title */
}

.font-bold {
    font-weight: bold; /* Bold font for titles */
}

.mb-4 {
    margin-bottom: 1rem; /* Margin below elements */
}

.space-y-4 > * + * {
    margin-top: 1rem; /* Space between form fields */
}

.block {
    display: block; /* Ensures label occupies whole line */
}

.font-semibold {
    font-weight: 600; /* Semi-bold for labels */
}

.border {
    border: 1px solid #ccc; /* Light gray border for inputs */
}

.p-3 {
    padding: 0.75rem; /* Padding for inputs */
}

.pr-10 {
    padding-right: 2.5rem; /* Padding for password input (to make space for the button) */
}

.relative {
    position: relative; /* Positioning for button inside input */
}

.absolute {
    position: absolute; /* Position button absolutely within relative input */
}

.right-3 {
    right: 0.75rem; /* Align button to right */
}

.top-3 {
    top: 50%; /* Center vertically */
    transform: translateY(-50%); /* Adjust for vertical alignment */
}

.text-sm {
    font-size: 0.875rem; /* Smaller font size for the button */
}

.bg-blue-500 {
    background-color: #3b82f6; /* Primary blue color */
}

.hover\:bg-blue-600:hover {
    background-color: #2563eb; /* Darker blue on hover */
}

.bg-gray-400 {
    background-color: #cbd5e1; /* Gray background for disabled state */
}

.cursor-not-allowed {
    cursor: not-allowed; /* Not allowed cursor when button is disabled */
}

.text-white {
    color: #ffffff; /* White text color */
}

.text-blue-500 {
    color: #3b82f6; /* Blue link color */
}

.hover\:underline:hover {
    text-decoration: underline; /* Underline on hover for links */
}

.mt-4 {
    margin-top: 1rem; /* Margin for spacing above elements */
}

/* Responsive Design */
@media (max-width: 640px) {
    .max-w-md {
        width: 100%; /* Full width on small screens */
    }
}