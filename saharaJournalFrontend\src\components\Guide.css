.guide-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background: #ffffff;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.guide-container h1 {
  color: #2c3e50;
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 3px solid #3498db;
}

.guide-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.guide-section h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.guide-section ul, .guide-section ol {
  padding-left: 1.5rem;
  line-height: 1.6;
}

.guide-section li {
  margin-bottom: 0.5rem;
  color: #34495e;
}

.example-box {
  background: #e8f4f8;
  padding: 1rem;
  border-left: 4px solid #3498db;
  margin-top: 1rem;
}

.example-box h3 {
  color: #2c3e50;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.step {
  text-align: center;
  padding: 1rem;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.step-number {
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  background: #3498db;
  color: white;
  border-radius: 50%;
  margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
  .guide-container {
    margin: 1rem;
    padding: 1rem;
  }
  
  .process-steps {
    grid-template-columns: 1fr;
  }
}
