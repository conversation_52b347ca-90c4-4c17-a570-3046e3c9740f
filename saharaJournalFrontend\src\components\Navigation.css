/* Main Navigation Styles */
.main-navigation {
  background-color: #1e3a8a;
  color: #ffffff;
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1000;
  transition: margin-left 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Sidebar margin for desktop */
@media (min-width: 768px) {
  .main-navigation {
    margin-left: 256px;
  }
  
  .sidebar-open .main-navigation {
    margin-left: 0;
  }
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0.5rem;
  max-width: 1400px;
  margin: 0 auto;
  height: 64px;
  width: 100%;
}

.nav-brand {
  display: flex;
  align-items: center;
  height: 100%;
  padding-right: 1rem;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

  .nav-logo {
    height: 80%;
    width: auto;
    max-height: 90%;
    padding: 0.5rem;
    border-radius: 4px;
  }

.nav-menu-container {
  display: flex;
  align-items: center;
  flex: 1;
  height: 100%;
  width: 100%;
  justify-content: space-between;
}

/* Navigation Links */
.nav-links {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  height: 100%;
}

.main-links {
  flex: 1;
  margin: 0 0.5rem;
}

.user-links {
  justify-content: flex-end;
  padding-left: 0.25rem;
  min-width: max-content;
  flex-shrink: 0;
}

.nav-divider {
  height: 50%;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.2);
  margin: 0 0.25rem;
}

.nav-links li {
  height: 100%;
  display: flex;
  align-items: center;
}

.user-links li {
  min-width: max-content;
}

.nav-links a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #ffffff;
  font-weight: 500;
  padding: 0 0.45rem;
  height: 100%;
  transition: background-color 0.2s ease, color 0.2s ease;
  position: relative;
  font-size: 0.85rem;
  white-space: nowrap;
  min-width: max-content;
  letter-spacing: -0.02em;
}

.nav-links a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-links a.active {
  color: #ffffff;
}

.nav-links a.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #2563eb;
}

/* Hamburger Menu */
.hamburger {
  display: none;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  z-index: 100;
  width: 48px;
  height: 48px;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.hamburger:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.hamburger:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* Enhanced hamburger icon styling */
.hamburger-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
}

/* Main menu toggle specific styles */
.main-menu-toggle {
  /* Inherits base hamburger styles */
}

/* Sidebar toggle specific styles - more prominent and distinguishable */
.sidebar-toggle {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  position: relative;
}

.sidebar-toggle:hover {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.sidebar-toggle:active {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: scale(0.95);
}

/* Add a small indicator that this is for the sidebar */
.sidebar-toggle::after {
  content: "";
  position: absolute;
  top: 8px;
  right: 8px;
  width: 6px;
  height: 6px;
  background-color: #fbbf24; /* amber color */
  border-radius: 50%;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2);
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .main-navigation {
    z-index: 1005;
  }

  .nav-container {
    padding: 0;
    height: 56px;
    display: grid;
    grid-template-columns: 48px 1fr 48px;
    align-items: center;
  }

  .nav-brand {
    grid-column: 2;
    justify-content: center;
    border-right: none;
    padding-right: 0;
  }

  .nav-logo {
    display: none;
  }

  .hamburger {
    display: flex;
    width: 40px;
    height: 40px;
    margin: 0 4px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  }

  .hamburger:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }

  .hamburger:active {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0.15) 100%);
    transform: scale(0.95);
  }

  .main-menu-toggle {
    grid-column: 1;
  }

  .sidebar-toggle {
    display: none;
  }

  .sidebar-toggle:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }

  .sidebar-toggle:active {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: scale(0.95);
  }

  .sidebar-toggle::after {
    content: "";
    position: absolute;
    top: 8px;
    right: 8px;
    width: 6px;
    height: 6px;
    background-color: #fbbf24; /* amber color */
    border-radius: 50%;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2);
  }

  .nav-menu-container {
    position: fixed;
    top: 56px;
    left: 0;
    right: 0;
    bottom: 0;
    flex-direction: column;
    background-color: #1e3a8a;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    align-items: flex-start;
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
  }

  .nav-menu-container.open {
    max-height: calc(100vh - 56px);
    overflow-y: auto;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  }

  .nav-links {
    flex-direction: column;
    width: 100%;
    height: auto;
    padding: 10px 0;
  }

  .main-links, 
  .user-links {
    margin: 0;
    padding: 0.5rem 0;
    width: 100%;
  }

  .nav-divider {
    width: 90%;
    height: 1px;
    margin: 0.5rem auto;
  }

  .nav-links li {
    width: 100%;
    height: auto;
    margin: 2px 0;
  }

  .nav-links a {
    padding: 0.85rem 1.5rem;
    height: auto;
    width: calc(100% - 16px);
    justify-content: flex-start;
    white-space: normal;
    line-height: 1.3;
    font-size: 1.05rem;
    text-align: left;
    min-width: auto;
    border-radius: 4px;
    margin: 0 8px;
    letter-spacing: normal;
  }

  .nav-links a.active::after {
    display: none;
  }

  .nav-links a.active {
    background-color: #2563eb;
  }

  .nav-links a.active::before {
    content: '•';
    position: absolute;
    left: 0.5rem;
    color: white;
  }
}

/* Tablet and Desktop Adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  .nav-links a {
    padding: 0 0.35rem;
    font-size: 0.8rem;
    letter-spacing: -0.025em;
  }

  .nav-container {
    padding: 0 0.5rem;
  }

  .nav-brand {
    padding-right: 0.5rem;
  }

  .main-links {
    margin: 0 0.25rem 0 0.25rem;
  }

  .nav-divider {
    margin: 0 0.15rem;
  }

  .user-links {
    padding-left: 0.15rem;
  }
}

@media (min-width: 1025px) {
  .nav-container {
    padding: 0 1.5rem;
  }

  .nav-brand {
    padding-right: 1.5rem;
  }

  .main-links {
    margin: 0 0.2rem 0 1rem;
  }

  .nav-divider {
    margin: 0 0.5rem;
  }

  .nav-links a {
    font-size: 0.9rem;
    padding: 0 0.5rem;
    letter-spacing: -0.01em;
  }

  .nav-links li {
    margin: 0 0.03rem;
  }

  .user-links {
    padding-left: 0.2rem;
  }
}
