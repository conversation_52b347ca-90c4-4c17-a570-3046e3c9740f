<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#1e3a8a" />
    
    <!-- Primary Meta Tags -->
    <meta
      name="description"
      content="Sahara International Journal of Teacher Education - Promoting excellence in teacher education through innovative research and scholarly publications."
    />
    <meta name="keywords" content="teacher education, educational research, academic journal, scholarly publications, education innovation, teaching methods, curriculum development, educational technology">
    <meta name="author" content="Sahara International Journal of Teacher Education">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.sijtejournal.com.ng/">
    <meta property="og:title" content="Sahara International Journal of Teacher Education">
    <meta property="og:description" content="Promoting excellence in teacher education through innovative research and scholarly publications.">
    <meta property="og:image" content="%PUBLIC_URL%/logo512.png">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://www.sijtejournal.com.ng/">
    <meta property="twitter:title" content="Sahara International Journal of Teacher Education">
    <meta property="twitter:description" content="Promoting excellence in teacher education through innovative research and scholarly publications.">
    <meta property="twitter:image" content="%PUBLIC_URL%/logo512.png">
    
    <!-- Google / Schema.org -->
    <meta itemprop="name" content="Sahara International Journal of Teacher Education">
    <meta itemprop="description" content="Promoting excellence in teacher education through innovative research and scholarly publications.">
    <meta itemprop="image" content="%PUBLIC_URL%/logo512.png">
    
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    
    <!-- Preload critical images -->
    <link rel="preload" href="%PUBLIC_URL%/images/image3.JPG" as="image" />
    <link rel="preload" href="%PUBLIC_URL%/images/image4.JPG" as="image" />
    <link rel="preload" href="%PUBLIC_URL%/images/image5.JPG" as="image" />

    <!-- Preload images with lowercase extensions as fallbacks -->
    <link rel="preload" href="%PUBLIC_URL%/images/image3.jpg" as="image" />
    <link rel="preload" href="%PUBLIC_URL%/images/image4.jpg" as="image" />
    <link rel="preload" href="%PUBLIC_URL%/images/image5.jpg" as="image" />
    
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Canonical URL - Updated to use environment-based URL -->
    <link rel="canonical" href="%PUBLIC_URL%/" />
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Sahara International Journal of Teacher Education</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
