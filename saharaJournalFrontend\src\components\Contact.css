.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.contact-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem 0;
}

.contact-header h1 {
  font-size: 2.5rem;
  color: #1a365d;
  margin-bottom: 1rem;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 3rem;
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.contact-info {
  padding: 2rem;
  background: #f8fafc;
  border-radius: 8px;
}

.info-section {
  margin-bottom: 2rem;
}

.info-section h3 {
  color: #1a365d;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 0.5rem;
}

.info-section p {
  margin: 1.2rem 0;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.1rem;
}

.info-section a {
  color: #2b6cb0;
  text-decoration: none;
  transition: color 0.3s ease;
}

.info-section a:hover {
  color: #1a365d;
}

.info-section i {
  color: #2b6cb0;
  width: 24px;
  font-size: 1.2rem;
}

.social-links h3 {
  margin-bottom: 1rem;
}

.social-icons {
  display: flex;
  gap: 1.2rem;
  flex-wrap: wrap;
}

.social-icons a {
  color: #2b6cb0;
  background-color: #f0f5ff;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-icons a:hover {
  color: white;
  background-color: #1a365d;
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.social-icons i {
  font-size: 1.2rem;
}

.contact-form {
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group textarea {
  resize: vertical;
}

.submit-btn {
  background: #1a365d;
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
}

.submit-btn:hover {
  background: #2a4365;
}

@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
  }
  
  .contact-header h1 {
    font-size: 2rem;
  }
}
