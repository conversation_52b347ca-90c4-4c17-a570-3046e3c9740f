# Sample environment variables file
# Copy this file to .env and fill in your actual values
# DO NOT commit the actual .env file with real credentials to the repository

# Node environment
NODE_ENV=development

# MongoDB connection
MONGODB_URI=<your-mongodb-connection-string>

# JWT secret for authentication
JWT_SECRET=<your-jwt-secret>

# Server port
PORT=5000

# Google Drive API credentials
GOOGLE_CLIENT_ID=<your-google-client-id>
GOOGLE_CLIENT_SECRET=<your-google-client-secret>
GOOGLE_REFRESH_TOKEN=<your-google-refresh-token>
GOOGLE_DRIVE_FOLDER_ID=<your-google-drive-folder-id>

# Document storage paths
DOCUMENT_STORAGE_PATH=../uploads/journals
DOCUMENT_STORAGE_URL=http://localhost:5000/uploads/journals
