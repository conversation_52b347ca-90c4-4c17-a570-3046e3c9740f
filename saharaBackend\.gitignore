# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Credentials and secrets
google-credentials.json
*credentials*.json
*.pem
*.key
*.keystore
*secret*
*token*
*oauth*

# Node modules
node_modules/
node_modules/*

# Build and output directories
dist/
build/

# Coverage directory used by testing tools like Jest
coverage/

# Cache files
.cache/
.temp/
*.tsbuildinfo

# Dependency directories
jspm_packages/

# IDE and editor folders
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln

# OS-specific files
.DS_Store
Thumbs.db

# TypeScript
*.tsbuildinfo

# NPM
package-lock.json
yarn.lock

# Docker
docker-compose.yml
Dockerfile

# Temporary files and backups
*.swp
*.bak
*.tmp
*.temp

# Debugger logs and profiler output
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# PM2 logs and process files
pm2-logs/
*.pm2

# MacOS files
._*

# Output from ESLint and Prettier
eslintcache
.prettiercache

# Local testing
*.local
*.test
*.spec
