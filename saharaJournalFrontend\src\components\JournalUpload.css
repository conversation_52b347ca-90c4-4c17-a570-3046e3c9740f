
/* Enhanced styling for Journal Upload Page */

/* Main container styling with improved gradient background */
.min-h-screen {
    min-height: 100vh;
    background: linear-gradient(to bottom right, #f0f4f8, #e6f0ff);
}

/* Content area styling */
.bg-gray-50 {
    background-color: #f9fafb;
}

.max-w-2xl {
    max-width: 42rem;
}

/* Card styling with enhanced shadow and subtle border */
.shadow-xl {
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 8px 10px -5px rgba(59, 130, 246, 0.04);
    transition: all 0.3s ease;
}

.bg-white {
    border: 1px solid rgba(226, 232, 240, 0.8);
}

/* Form section styling */
.p-6 {
    padding: 1.75rem;
}

/* Header styling */
.text-2xl {
    font-size: 1.5rem;
    color: #1e3a8a;
    letter-spacing: -0.025em;
}

/* Input fields with improved focus states */
.border-gray-300 {
    border-color: #d1d5db;
    transition: all 0.2s ease;
}

input[type="text"] {
    padding: 0.625rem 1rem;
    border-radius: 0.375rem;
    background-color: #f9fafb;
}

.focus\:ring-blue-500:focus {
    --tw-ring-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.focus\:border-blue-500:focus {
    border-color: #3b82f6;
}

/* Form labels */
.text-sm.font-medium {
    color: #4b5563;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Helper text */
.text-sm.text-gray-500 {
    color: #6b7280;
    font-size: 0.813rem;
}

/* File upload area with improved visual feedback */
.border-dashed {
    border-style: dashed;
    border-width: 2px;
    border-color: #d1d5db;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.border-dashed:hover {
    border-color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.05);
}

/* Upload icon */
svg.text-gray-400 {
    color: #9ca3af;
    transition: color 0.2s ease;
}

.border-dashed:hover svg.text-gray-400 {
    color: #3b82f6;
}

/* Text link styling */
.text-blue-600 {
    color: #2563eb;
    font-weight: 500;
    transition: color 0.2s ease;
}

.hover\:text-blue-500:hover {
    color: #3b82f6;
    text-decoration: underline;
}

/* Button styling with improved visual feedback */
button {
    font-weight: 500;
    transition: all 0.2s ease;
    border-radius: 0.375rem;
    padding: 0.5rem 1.25rem;
}

/* Reset button */
button[type="button"] {
    border-color: #d1d5db;
    color: #4b5563;
}

button[type="button"]:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

/* Submit button */
.bg-blue-600 {
    background-color: #2563eb;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.hover\:bg-blue-700:hover {
    background-color: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

button:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Status messages */
.bg-red-50 {
    background-color: #fef2f2;
    border-color: #ef4444;
}

.bg-green-50 {
    background-color: #ecfdf5;
    border-color: #10b981;
}

.text-red-700 {
    color: #b91c1c;
}

.text-green-700 {
    color: #047857;
}

/* Loading spinner with improved animation */
.animate-spin {
    animation: spin 1s cubic-bezier(0.55, 0.055, 0.675, 0.19) infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .px-4 {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }
    
    .shadow-xl {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    .p-6 {
        padding: 1.25rem;
    }
}

/* Form validation visual feedback */
input:required:invalid:focus {
    border-color: #f87171;
    box-shadow: 0 0 0 3px rgba(248, 113, 113, 0.2);
}

input:valid:focus {
    border-color: #34d399;
    box-shadow: 0 0 0 3px rgba(52, 211, 153, 0.2);
}

/* Improved spacing between form fields */
.space-y-6 > div {
    margin-bottom: 1.5rem;
}

/* Progress indicators for multi-step forms (if you decide to implement) */
.progress-bar {
    height: 0.25rem;
    background-color: #e5e7eb;
    border-radius: 9999px;
    overflow: hidden;
    margin-bottom: 2rem;
}

.progress-bar-filled {
    height: 100%;
    background-color: #3b82f6;
    transition: width 0.3s ease;
}