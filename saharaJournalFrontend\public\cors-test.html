<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test - Sahara Journal</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e3a8a;
            text-align: center;
        }
        .test-result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #1e3a8a;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            display: block;
            margin: 20px auto;
        }
        button:hover {
            background-color: #2563eb;
        }
        .endpoint {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CORS Configuration Test</h1>
        <p>This page tests if your CORS configuration is working correctly with your backend API.</p>
        
        <div class="endpoint">
            Testing endpoint: <strong>https://saharabackend-v190.onrender.com/api/journals?page=1&limit=6&sortBy=createdAt&order=desc</strong>
        </div>
        
        <button onclick="testCORS()">Test CORS Request</button>
        
        <div id="result"></div>
        
        <h2>Instructions:</h2>
        <ol>
            <li>Make sure your backend is running</li>
            <li>Click the "Test CORS Request" button</li>
            <li>Check the result below</li>
            <li>If you see a success message, CORS is configured correctly</li>
            <li>If you see an error, check your backend configuration</li>
        </ol>
    </div>

    <script>
        async function testCORS() {
            const resultDiv = document.getElementById('result');
            const endpoint = 'https://saharabackend-v190.onrender.com/api/journals?page=1&limit=6&sortBy=createdAt&order=desc';
            
            try {
                resultDiv.innerHTML = '<div class="test-result">Testing CORS request...</div>';
                
                const response = await fetch(endpoint, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ CORS Test Successful!<br>
                            Status: ${response.status}<br>
                            Journals found: ${data.journals ? data.journals.length : 'N/A'}<br>
                            Message: ${data.message || 'Request completed successfully'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ CORS Test Failed!<br>
                            Status: ${response.status}<br>
                            Status Text: ${response.statusText}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('CORS Test Error:', error);
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ CORS Test Failed!<br>
                        Error: ${error.message}<br>
                        This indicates a CORS configuration issue.
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
