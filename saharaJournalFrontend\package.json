{"name": "saharafrontend", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.9.7", "@testing-library/dom": "^9.3.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.7", "bootstrap": "^5.3.3", "cookie": "^1.0.2", "formik": "^2.4.6", "framer-motion": "^10.16.4", "immer": "^10.1.1", "install": "^0.13.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-helmet": "^6.1.0", "react-icons": "^4.12.0", "react-redux": "^8.1.3", "react-router-dom": "^6.22.3", "react-scripts": "5.0.1", "react-tabs": "^6.1.0", "react-toastify": "^9.1.3", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && node copy-images-to-build.js", "test": "react-scripts test", "eject": "react-scripts eject", "copy-images": "node copy-images-to-build.js", "generate-sitemap": "node scripts/generate-sitemap.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}