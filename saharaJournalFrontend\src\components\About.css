.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: '<PERSON><PERSON>', sans-serif;
}

.about-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem 0;
  background: linear-gradient(to right, #1a365d, #2a4365);
  color: white;
  border-radius: 8px;
}

.about-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
}

.about-section {
  margin-bottom: 4rem;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.vision-mission {
  display: flex;
  gap: 2rem;
}

.vision, .mission {
  flex: 1;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 8px;
}

.institution-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.location-map {
  width: 100%;
  height: 450px;
  border-radius: 8px;
  overflow: hidden;
}

.journal-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.detail-card {
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #1a365d;
}

.contact-details {
  margin-top: 2rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .vision-mission,
  .institution-content {
    grid-template-columns: 1fr;
  }
  
  .about-header h1 {
    font-size: 2rem;
  }
}

h2 {
  color: #1a365d;
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

h3 {
  color: #2a4365;
  margin-bottom: 1rem;
  font-size: 1.4rem;
}

p {
  line-height: 1.6;
  color: #4a5568;
}
