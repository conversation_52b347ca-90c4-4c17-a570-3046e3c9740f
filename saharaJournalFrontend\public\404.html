<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Page not found - Sahara International Journal of Teacher Education">
    <title>404 - Page Not Found | Sahara International Journal of Teacher Education</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            text-align: center;
            max-width: 800px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 40px;
        }
        
        .error-code {
            font-size: 100px;
            font-weight: 700;
            color: #1e3a8a;
            margin-bottom: 20px;
        }
        
        h1 {
            font-size: 36px;
            margin-bottom: 20px;
            color: #1e3a8a;
        }
        
        p {
            font-size: 18px;
            margin-bottom: 30px;
            color: #666;
        }
        
        .home-link {
            display: inline-block;
            background-color: #1e3a8a;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 600;
            transition: background-color 0.3s;
        }
        
        .home-link:hover {
            background-color: #2563eb;
        }
        
        .search-box {
            margin: 30px 0;
        }
        
        .search-box input {
            padding: 12px;
            width: 60%;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .search-box button {
            padding: 12px 20px;
            background-color: #1e3a8a;
            color: white;
            border: none;
            border-radius: 5px;
            margin-left: 10px;
            cursor: pointer;
            font-size: 16px;
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 80px;
            }
            
            h1 {
                font-size: 28px;
            }
            
            .search-box input {
                width: 100%;
                margin-bottom: 10px;
            }
            
            .search-box button {
                margin-left: 0;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-code">404</div>
        <h1>Page Not Found</h1>
        <p>Sorry, the page you are looking for might have been removed, had its name changed, or is temporarily unavailable.</p>
        
        <div class="search-box">
            <input type="text" id="search-input" placeholder="Search our website...">
            <button onclick="performSearch()"><i class="fas fa-search"></i> Search</button>
        </div>
        
        <a href="/" class="home-link"><i class="fas fa-home"></i> Go to Homepage</a>
    </div>

    <script>
        function performSearch() {
            const searchTerm = document.getElementById('search-input').value;
            if (searchTerm.trim() !== '') {
                // In a real implementation, this would redirect to a search results page
                alert('In a full implementation, this would search for: ' + searchTerm);
            }
        }
        
        // Allow pressing Enter in search box
        document.getElementById('search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    </script>
</body>
</html>
